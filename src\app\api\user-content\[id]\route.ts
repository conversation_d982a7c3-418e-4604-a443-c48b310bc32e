import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// GET /api/user-content/[id] - Get specific user content by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const { id: contentId } = await params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(contentId)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid content ID format' 
        },
        { status: 400 }
      );
    }

    // Get user content with related data
    const userContent = await prisma.userContent.findFirst({
      where: {
        id: contentId,
        user_id: user.id // Ensure user owns this content
      },
      include: {
        challenge: {
          select: {
            id: true,
            title: true,
            description: true,
            type: true,
            difficulty: true,
            prompt: true
          }
        },
        challenge_completion: {
          select: {
            id: true,
            completed_at: true
          }
        }
      }
    });

    if (!userContent) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Content not found or access denied' 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      content: userContent
    });

  } catch (error) {
    console.error('Error fetching user content:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch content' 
      },
      { status: 500 }
    );
  }
}

// PUT /api/user-content/[id] - Update user content
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const { id: contentId } = await params;
    const { title, content_metadata } = await request.json();

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(contentId)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid content ID format' 
        },
        { status: 400 }
      );
    }

    // Check if content exists and belongs to user
    const existingContent = await prisma.userContent.findFirst({
      where: {
        id: contentId,
        user_id: user.id
      }
    });

    if (!existingContent) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Content not found or access denied' 
        },
        { status: 404 }
      );
    }

    // Update the content
    const updatedContent = await prisma.userContent.update({
      where: {
        id: contentId
      },
      data: {
        title: title || existingContent.title,
        content_metadata: content_metadata || existingContent.content_metadata,
        updated_at: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Content updated successfully',
      content: {
        id: updatedContent.id,
        title: updatedContent.title,
        type: updatedContent.type,
        updated_at: updatedContent.updated_at
      }
    });

  } catch (error) {
    console.error('Error updating user content:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update content' 
      },
      { status: 500 }
    );
  }
}

// DELETE /api/user-content/[id] - Delete user content
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const contentId = params.id;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(contentId)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid content ID format' 
        },
        { status: 400 }
      );
    }

    // Check if content exists and belongs to user
    const existingContent = await prisma.userContent.findFirst({
      where: {
        id: contentId,
        user_id: user.id
      }
    });

    if (!existingContent) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Content not found or access denied' 
        },
        { status: 404 }
      );
    }

    // Delete the content
    await prisma.userContent.delete({
      where: {
        id: contentId
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Content deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting user content:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete content' 
      },
      { status: 500 }
    );
  }
}
