"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, Share2, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { toast } from "sonner";
import ProjectsFilters from "@/components/projects/ProjectsFilters";
import ProjectsGrid from "@/components/projects/ProjectsGrid";
import CreativeGrowthTracker from "@/components/projects/CreativeGrowthTracker";
import CreativeGrowthTimeline from "@/components/projects/CreativeGrowthTimeline";
import ShareWithParentsModal from "@/components/projects/ShareWithParentsModal";

interface UserContent {
    id: string;
    type: string;
    title: string;
    content_metadata: Record<string, unknown>;
    preview_url: string | null;
    challenge_id: string | null;
    created_at: string;
    updated_at: string;
    challenge?: {
        id: string;
        title: string;
        type: string;
        difficulty: string;
    };
}

interface ContentStats {
    stories: number;
    artwork: number;
    music: number;
    total: number;
}

const MyProjectsPage = () => {
    const { user, loading: authLoading } = useAuth();
    const router = useRouter();
    const [projects, setProjects] = useState<UserContent[]>([]);
    const [filteredProjects, setFilteredProjects] = useState<UserContent[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [selectedFilter, setSelectedFilter] = useState<string>("all");
    const [sortBy, setSortBy] = useState<string>("newest");
    const [contentStats, setContentStats] = useState<ContentStats>({
        stories: 0,
        artwork: 0,
        music: 0,
        total: 0
    });
    const [showShareModal, setShowShareModal] = useState(false);

    // Redirect to auth if not authenticated
    useEffect(() => {
        if (!authLoading && !user) {
            router.push("/auth");
        }
    }, [user, authLoading, router]);

    // Fetch user's projects
    const fetchProjects = React.useCallback(async () => {
        if (!user) return;

        setIsLoading(true);
        try {
            const response = await fetch('/api/user-content', {
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                const userContent = data.content || [];
                console.log('Fetched projects:', userContent);
                setProjects(userContent);
                setFilteredProjects(userContent);

                // Calculate content statistics
                const stats = userContent.reduce((acc: ContentStats, content: UserContent) => {
                    acc.total++;
                    switch (content.type) {
                        case 'story':
                            acc.stories++;
                            break;
                        case 'art':
                            acc.artwork++;
                            break;
                        case 'music':
                            acc.music++;
                            break;
                    }
                    return acc;
                }, { stories: 0, artwork: 0, music: 0, total: 0 });

                console.log('Content stats:', stats);
                setContentStats(stats);
            } else {
                console.error('Failed to fetch projects:', data.error);
                toast.error('Failed to load projects');
            }
        } catch (error) {
            console.error('Error fetching projects:', error);
            toast.error('Failed to load projects');
        } finally {
            setIsLoading(false);
        }
    }, [user]);

    // Filter and sort projects
    useEffect(() => {
        console.log('Filtering and sorting projects:', { selectedFilter, sortBy, projectsCount: projects.length });

        let filtered = projects;

        // Apply filter
        if (selectedFilter !== "all") {
            filtered = projects.filter(project => {
                const matches = project.type === selectedFilter;
                console.log(`Project "${project.title}" (type: ${project.type}) matches filter "${selectedFilter}":`, matches);
                return matches;
            });
        }

        // Apply sort
        const sorted = [...filtered].sort((a, b) => {
            switch (sortBy) {
                case "newest":
                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
                case "oldest":
                    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
                case "a-z":
                    return a.title.localeCompare(b.title);
                default:
                    return 0;
            }
        });

        console.log('Filtered and sorted projects:', sorted.length);
        setFilteredProjects(sorted);
    }, [selectedFilter, sortBy, projects]);

    // Fetch projects when user is available
    useEffect(() => {
        if (user && !authLoading) {
            fetchProjects();
        }
    }, [user, authLoading, fetchProjects]);

    const handleShareWithParents = () => {
        setShowShareModal(true);
    };

    const handleBackToDashboard = () => {
        router.push("/dashboard");
    };

    // Show loading state
    if (authLoading || isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading your creative projects...</p>
                </div>
            </div>
        );
    }

    // Don't render if no user
    if (!user) {
        return null;
    }

    return (
        <div className="min-h-screen bg-white">
            <div className="container max-w-4xl mx-auto px-6 py-8">
                {/* Header */}
                <div className="flex items-center justify-between mb-8">
                    <Button
                        variant="ghost"
                        onClick={handleBackToDashboard}
                        className="text-gray-600 hover:text-gray-800 p-0"
                    >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to Dashboard
                    </Button>
                </div>

                {/* Projects Header Card */}
                <Card className="p-6 mb-8 bg-gradient-to-r from-blue-50 via-purple-50 to-teal-50 border-0 shadow-lg">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg">
                                <Sparkles className="h-6 w-6 text-white" />
                            </div>
                            <div>
                                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent">
                                    My Creative Projects
                                </h1>
                                <p className="text-gray-600 mt-1 flex items-center">
                                    <Trophy className="h-4 w-4 mr-1 text-yellow-500" />
                                    Showcase your amazing creations
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-3">
                            <div className="hidden sm:flex items-center space-x-2 bg-white/70 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20">
                                <Star className="h-4 w-4 text-yellow-500" />
                                <span className="text-sm font-medium text-gray-700">
                                    {projects.length} Projects
                                </span>
                            </div>
                            <Button
                                onClick={handleShareWithParents}
                                className="bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                            >
                                <Share2 className="h-4 w-4 mr-2" />
                                Share With Parents
                            </Button>
                        </div>
                    </div>
                </Card>

                {/* Filters */}
                <ProjectsFilters
                    selectedFilter={selectedFilter}
                    onFilterChange={setSelectedFilter}
                    contentStats={contentStats}
                    sortBy={sortBy}
                    onSortChange={setSortBy}
                />

                {/* Projects Grid */}
                <ProjectsGrid
                    projects={filteredProjects}
                    isLoading={isLoading}
                    selectedFilter={selectedFilter}
                />

                {/* Creative Growth Tracker */}
                <CreativeGrowthTracker contentStats={contentStats} />

                {/* Creative Growth Timeline - only show if there are projects */}
                {projects.length > 0 && (
                    <CreativeGrowthTimeline projects={projects} />
                )}

                {/* Share With Parents Modal */}
                <ShareWithParentsModal
                    isOpen={showShareModal}
                    onClose={() => setShowShareModal(false)}
                    userStats={contentStats}
                />
            </div>
        </div>
    );
};

export default MyProjectsPage;
