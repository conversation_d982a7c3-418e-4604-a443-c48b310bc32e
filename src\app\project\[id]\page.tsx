"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useAuth } from "@/hooks/useAuth";
import { useRouter, useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { 
    ArrowLeft, 
    BookOpen, 
    Palette, 
    Music, 
    Download, 
    Share2, 
    Edit3,
    Calendar,
    Award,
    Loader
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";

interface UserContent {
    id: string;
    type: string;
    title: string;
    content_metadata: Record<string, unknown>;
    preview_url: string | null;
    challenge_id: string | null;
    created_at: string;
    updated_at: string;
    challenge?: {
        id: string;
        title: string;
        description: string;
        type: string;
        difficulty: string;
        prompt: string;
    };
    challenge_completion?: {
        id: string;
        completed_at: string;
    };
}

const ProjectDetailPage = () => {
    const { user, loading: authLoading } = useAuth();
    const router = useRouter();
    const params = useParams();
    const projectId = params.id as string;
    
    const [project, setProject] = useState<UserContent | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isEditing, setIsEditing] = useState(false);
    const [editedTitle, setEditedTitle] = useState("");

    // Redirect to auth if not authenticated
    useEffect(() => {
        if (!authLoading && !user) {
            router.push("/auth");
        }
    }, [user, authLoading, router]);

    // Fetch project details
    useEffect(() => {
        if (user && projectId) {
            fetchProjectDetails();
        }
    }, [user, projectId, fetchProjectDetails]);

    const fetchProjectDetails = React.useCallback(async () => {
        setIsLoading(true);
        try {
            const response = await fetch(`/api/user-content/${projectId}`, {
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                setProject(data.content);
                setEditedTitle(data.content.title);
            } else {
                toast.error('Project not found');
                router.push('/my-projects');
            }
        } catch (error) {
            console.error('Error fetching project details:', error);
            toast.error('Failed to load project');
            router.push('/my-projects');
        } finally {
            setIsLoading(false);
        }
    }, [projectId, router]);

    const handleSaveTitle = async () => {
        if (!project || !editedTitle.trim()) return;

        try {
            const response = await fetch(`/api/user-content/${project.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    title: editedTitle.trim()
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                setProject({ ...project, title: editedTitle.trim() });
                setIsEditing(false);
                toast.success('Project title updated successfully');
            } else {
                toast.error('Failed to update project title');
            }
        } catch (error) {
            console.error('Error updating project title:', error);
            toast.error('Failed to update project title');
        }
    };

    const handleShare = async () => {
        if (!project) return;

        try {
            const response = await fetch('/api/user-content/share', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    contentId: project.id
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.shareUrl) {
                await navigator.clipboard.writeText(data.shareUrl);
                toast.success('Share link copied to clipboard!');
            } else {
                toast.error('Failed to generate share link');
            }
        } catch (error) {
            console.error('Error sharing project:', error);
            toast.error('Failed to share project');
        }
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case "story":
                return <BookOpen className="h-6 w-6 text-green-600" />;
            case "art":
                return <Palette className="h-6 w-6 text-green-600" />;
            case "music":
                return <Music className="h-6 w-6 text-green-600" />;
            default:
                return <BookOpen className="h-6 w-6 text-green-600" />;
        }
    };

    const getDifficultyColor = (difficulty: string) => {
        switch (difficulty?.toLowerCase()) {
            case "easy":
                return "bg-green-100 text-green-800";
            case "medium":
                return "bg-yellow-100 text-yellow-800";
            case "hard":
                return "bg-red-100 text-red-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    // Show loading state
    if (authLoading || isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <Loader className="h-12 w-12 animate-spin text-green-500 mx-auto mb-4" />
                    <p className="text-gray-600">Loading project...</p>
                </div>
            </div>
        );
    }

    // Don't render if no user
    if (!user || !project) {
        return null;
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-4xl mx-auto">
                    {/* Header */}
                    <div className="flex items-center gap-4 mb-6">
                        <Button
                            onClick={() => router.push('/my-projects')}
                            variant="outline"
                            size="sm"
                            className="hover:bg-green-50 hover:border-green-300"
                        >
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Projects
                        </Button>
                    </div>

                    {/* Project Card */}
                    <Card className="p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                        {/* Project Header */}
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center gap-4">
                                <div className="p-3 bg-green-100 rounded-lg">
                                    {getTypeIcon(project.type)}
                                </div>
                                <div>
                                    {isEditing ? (
                                        <div className="flex items-center gap-2">
                                            <input
                                                type="text"
                                                value={editedTitle}
                                                onChange={(e) => setEditedTitle(e.target.value)}
                                                placeholder="Enter project title"
                                                className="text-2xl font-bold text-gray-900 bg-transparent border-b-2 border-green-300 focus:border-green-500 outline-none"
                                                onKeyPress={(e) => e.key === 'Enter' && handleSaveTitle()}
                                            />
                                            <Button onClick={handleSaveTitle} size="sm" className="bg-green-600 hover:bg-green-700">
                                                Save
                                            </Button>
                                            <Button onClick={() => setIsEditing(false)} variant="outline" size="sm">
                                                Cancel
                                            </Button>
                                        </div>
                                    ) : (
                                        <h1 className="text-2xl font-bold text-gray-900">{project.title}</h1>
                                    )}
                                    <div className="flex items-center gap-2 mt-2">
                                        <span className="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                            {project.type.charAt(0).toUpperCase() + project.type.slice(1)}
                                        </span>
                                        {project.challenge && (
                                            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(project.challenge.difficulty)}`}>
                                                {project.challenge.difficulty}
                                            </span>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Project Content */}
                        <div className="mb-6">
                            {renderProjectContent()}
                        </div>

                        {/* Project Info */}
                        <div className="space-y-4 mb-6 p-4 bg-gray-50 rounded-lg">
                            {project.challenge && (
                                <div className="flex items-start gap-3">
                                    <Award className="h-5 w-5 text-green-600 mt-0.5" />
                                    <div>
                                        <h4 className="font-semibold text-gray-900">Challenge</h4>
                                        <p className="text-gray-700">{project.challenge.title}</p>
                                        {project.challenge.description && (
                                            <p className="text-sm text-gray-600 mt-1">{project.challenge.description}</p>
                                        )}
                                    </div>
                                </div>
                            )}
                            <div className="flex items-center gap-3">
                                <Calendar className="h-5 w-5 text-green-600" />
                                <div>
                                    <h4 className="font-semibold text-gray-900">Created</h4>
                                    <p className="text-gray-700">{format(new Date(project.created_at), 'PPP')}</p>
                                </div>
                            </div>
                        </div>

                        {/* Actions */}
                        <div className="flex flex-wrap gap-3 pt-4 border-t">
                            <Button
                                onClick={() => setIsEditing(true)}
                                variant="outline"
                                className="hover:bg-green-50 hover:border-green-300"
                            >
                                <Edit3 className="h-4 w-4 mr-2" />
                                Edit Title
                            </Button>
                            <Button
                                onClick={handleShare}
                                variant="outline"
                                className="hover:bg-green-50 hover:border-green-300"
                            >
                                <Share2 className="h-4 w-4 mr-2" />
                                Share
                            </Button>
                            {project.preview_url && (
                                <Button
                                    onClick={() => window.open(project.preview_url!, '_blank')}
                                    variant="outline"
                                    className="hover:bg-green-50 hover:border-green-300"
                                >
                                    <Download className="h-4 w-4 mr-2" />
                                    Download
                                </Button>
                            )}
                        </div>
                    </Card>
                </div>
            </div>
        </div>
    );

    function renderProjectContent() {
        if (!project) return null;

        switch (project.type) {
            case "story":
                return (
                    <div className="space-y-4">
                        {project.content_metadata?.story && (
                            <div className="bg-green-50 p-6 rounded-lg">
                                <h4 className="font-semibold text-green-900 mb-3">Story Content</h4>
                                <div className="prose prose-green max-w-none">
                                    <p className="text-green-800 whitespace-pre-wrap leading-relaxed">
                                        {project.content_metadata.story}
                                    </p>
                                </div>
                            </div>
                        )}
                        {project.content_metadata?.prompt && (
                            <div className="bg-blue-50 p-4 rounded-lg">
                                <h4 className="font-semibold text-blue-900 mb-2">Story Prompt</h4>
                                <p className="text-blue-800">{project.content_metadata.prompt}</p>
                            </div>
                        )}
                    </div>
                );

            case "art":
                return (
                    <div className="space-y-4">
                        {project.preview_url ? (
                            <div className="bg-gray-100 rounded-lg overflow-hidden">
                                <Image
                                    src={project.preview_url}
                                    alt={project.title}
                                    width={800}
                                    height={400}
                                    className="w-full h-auto max-h-96 object-contain hover:scale-105 transition-transform duration-300"
                                />
                            </div>
                        ) : (
                            <div className="bg-gray-100 rounded-lg p-12 text-center">
                                <Palette className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                <p className="text-gray-500">Artwork preview not available</p>
                            </div>
                        )}
                        {project.content_metadata?.prompt && (
                            <div className="bg-green-50 p-4 rounded-lg">
                                <h4 className="font-semibold text-green-900 mb-2">Art Prompt</h4>
                                <p className="text-green-800">{project.content_metadata.prompt}</p>
                            </div>
                        )}
                    </div>
                );

            case "music":
                return (
                    <div className="space-y-4">
                        {project.preview_url ? (
                            <div className="bg-gradient-to-br from-green-100 to-green-200 rounded-lg p-8">
                                <div className="text-center mb-4">
                                    <Music className="h-16 w-16 text-green-600 mx-auto mb-4" />
                                    <h4 className="font-semibold text-green-900">Music Track</h4>
                                </div>
                                <audio
                                    controls
                                    className="w-full"
                                    src={project.preview_url}
                                >
                                    Your browser does not support the audio element.
                                </audio>
                            </div>
                        ) : (
                            <div className="bg-gray-100 rounded-lg p-12 text-center">
                                <Music className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                <p className="text-gray-500">Music preview not available</p>
                            </div>
                        )}
                        {project.content_metadata?.description && (
                            <div className="bg-green-50 p-4 rounded-lg">
                                <h4 className="font-semibold text-green-900 mb-2">Music Description</h4>
                                <p className="text-green-800">{project.content_metadata.description}</p>
                            </div>
                        )}
                        {project.content_metadata?.mood && (
                            <div className="bg-purple-50 p-4 rounded-lg">
                                <h4 className="font-semibold text-purple-900 mb-2">Mood & Style</h4>
                                <p className="text-purple-800">{project.content_metadata.mood}</p>
                            </div>
                        )}
                    </div>
                );

            default:
                return (
                    <div className="bg-gray-100 rounded-lg p-8 text-center">
                        <p className="text-gray-500">Content preview not available</p>
                    </div>
                );
        }
    }
};

export default ProjectDetailPage;
