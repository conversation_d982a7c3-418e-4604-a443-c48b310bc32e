"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { Card } from "@/components/ui/card";
import { BookOpen, Palette, Music, Calendar, Award, Heart, Star } from "lucide-react";
import { format } from "date-fns";

interface UserContent {
    id: string;
    type: string;
    title: string;
    content_metadata: Record<string, unknown>;
    preview_url: string | null;
    created_at: string;
    challenge?: {
        title: string;
        type: string;
        difficulty: string;
    };
}

interface SharedPortfolioData {
    childName: string;
    projects: UserContent[];
    stats: {
        stories: number;
        artwork: number;
        music: number;
        total: number;
    };
    message?: string;
    sharedAt: string;
}

export default function SharedPortfolioPage({ params }: { params: { token: string } }) {
    const [portfolioData, setPortfolioData] = useState<SharedPortfolioData | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        fetchPortfolioData();
    }, [params.token, fetchPortfolioData]);

    const fetchPortfolioData = React.useCallback(async () => {
        try {
            const response = await fetch(`/api/shared/portfolio/${params.token}`);
            const data = await response.json();

            if (data.success) {
                setPortfolioData(data.portfolio);
            } else {
                setError(data.error || 'Portfolio not found');
            }
        } catch (error) {
            console.error('Error fetching portfolio:', error);
            setError('Failed to load portfolio');
        } finally {
            setIsLoading(false);
        }
    }, [params.token]);

    const getTypeIcon = (type: string) => {
        switch (type) {
            case "story":
                return <BookOpen className="h-5 w-5 text-blue-600" />;
            case "art":
                return <Palette className="h-5 w-5 text-green-600" />;
            case "music":
                return <Music className="h-5 w-5 text-purple-600" />;
            default:
                return <BookOpen className="h-5 w-5 text-gray-600" />;
        }
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-teal-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-500 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading creative portfolio...</p>
                </div>
            </div>
        );
    }

    if (error || !portfolioData) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-teal-50 flex items-center justify-center">
                <Card className="p-8 text-center max-w-md">
                    <div className="text-red-500 mb-4">
                        <Calendar className="h-12 w-12 mx-auto" />
                    </div>
                    <h1 className="text-xl font-bold text-gray-900 mb-2">Portfolio Not Available</h1>
                    <p className="text-gray-600">{error}</p>
                </Card>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-teal-50">
            <div className="container max-w-4xl mx-auto px-6 py-8">
                {/* Header */}
                <div className="text-center mb-8">
                    <div className="flex items-center justify-center gap-2 mb-4">
                        <Star className="h-8 w-8 text-yellow-500" />
                        <h1 className="text-3xl font-bold text-gray-900">
                            {portfolioData.childName}&apos;s Creative Portfolio
                        </h1>
                        <Star className="h-8 w-8 text-yellow-500" />
                    </div>
                    <p className="text-gray-600">Shared from Little Spark - Where Creativity Comes to Life!</p>
                    <p className="text-sm text-gray-500 mt-2">
                        Shared on {format(new Date(portfolioData.sharedAt), "MMMM d, yyyy 'at' h:mm a")}
                    </p>
                </div>

                {/* Personal Message */}
                {portfolioData.message && (
                    <Card className="p-6 mb-8 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
                        <div className="flex items-center gap-2 mb-3">
                            <Heart className="h-5 w-5 text-red-500" />
                            <h2 className="font-semibold text-gray-900">Personal Message</h2>
                        </div>
                        <p className="text-gray-700 italic">&ldquo;{portfolioData.message}&rdquo;</p>
                    </Card>
                )}

                {/* Statistics */}
                <Card className="p-6 mb-8">
                    <h2 className="text-xl font-bold text-gray-900 mb-6 text-center">Creative Achievements</h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                        <div className="text-center">
                            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <BookOpen className="h-8 w-8 text-blue-600" />
                            </div>
                            <div className="text-2xl font-bold text-blue-600">{portfolioData.stats.stories}</div>
                            <div className="text-sm text-gray-600">Stories</div>
                        </div>
                        <div className="text-center">
                            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <Palette className="h-8 w-8 text-green-600" />
                            </div>
                            <div className="text-2xl font-bold text-green-600">{portfolioData.stats.artwork}</div>
                            <div className="text-sm text-gray-600">Artwork</div>
                        </div>
                        <div className="text-center">
                            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <Music className="h-8 w-8 text-purple-600" />
                            </div>
                            <div className="text-2xl font-bold text-purple-600">{portfolioData.stats.music}</div>
                            <div className="text-sm text-gray-600">Music</div>
                        </div>
                        <div className="text-center">
                            <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <Star className="h-8 w-8 text-orange-600" />
                            </div>
                            <div className="text-2xl font-bold text-orange-600">{portfolioData.stats.total}</div>
                            <div className="text-sm text-gray-600">Total Projects</div>
                        </div>
                    </div>
                </Card>

                {/* Projects Grid */}
                {portfolioData.projects.length > 0 && (
                    <Card className="p-6 mb-8">
                        <h2 className="text-xl font-bold text-gray-900 mb-6">Recent Creative Projects</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {portfolioData.projects.map((project) => (
                                <div key={project.id} className="bg-gray-50 rounded-lg p-4">
                                    <div className="flex items-center gap-3 mb-3">
                                        {getTypeIcon(project.type)}
                                        <h3 className="font-semibold text-gray-900">{project.title}</h3>
                                    </div>
                                    
                                    {project.preview_url && project.type === 'art' && (
                                        <div className="mb-3">
                                            <Image
                                                src={project.preview_url}
                                                alt={project.title}
                                                width={400}
                                                height={128}
                                                className="w-full h-32 object-cover rounded-lg"
                                            />
                                        </div>
                                    )}
                                    
                                    <div className="flex items-center justify-between text-sm text-gray-600">
                                        <span className="bg-white px-2 py-1 rounded-full">
                                            {project.type.charAt(0).toUpperCase() + project.type.slice(1)}
                                        </span>
                                        {project.challenge && (
                                            <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full flex items-center gap-1">
                                                <Award className="h-3 w-3" />
                                                Challenge
                                            </span>
                                        )}
                                        <span>{format(new Date(project.created_at), "MMM d")}</span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </Card>
                )}

                {/* Footer */}
                <div className="text-center py-8">
                    <div className="bg-white rounded-lg p-6 shadow-sm">
                        <h3 className="font-semibold text-gray-900 mb-2">About Little Spark</h3>
                        <p className="text-gray-600 text-sm mb-4">
                            Little Spark is a creative platform where children can write stories, create artwork, 
                            compose music, and participate in fun challenges. It&apos;s designed to nurture creativity,
                            imagination, and self-expression in a safe, engaging environment.
                        </p>
                        <a 
                            href={process.env.NEXT_PUBLIC_SITE_URL} 
                            className="text-teal-600 hover:text-teal-700 font-medium"
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            Visit Little Spark →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    );
}
