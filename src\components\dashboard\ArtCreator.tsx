"use client";
import React from "react";
import ArtHeader from "@/components/art/ArtHeader";
import ArtCreatorContent from "@/components/art/ArtCreatorContent";
import {
  useArtGeneration,
  useLearningMode,
  usePortfolioSave,
  useArtStyle,
} from "@/hooks/art";
import { useArtPrompt } from "@/hooks/art/useArtPrompt";

const ArtCreator = () => {
  // Import art style hook
  const { artStyle, setArtStyle } = useArtStyle();

  // Extract art prompt functionality (needs artStyle)
  const {
    prompt,
    setPrompt,
    title,
    setTitle,
    isGeneratingIdea,
    isImprovingPrompt,
    handleGenerateIdea,
    handleImprovePrompt,
  } = useArtPrompt(artStyle);

  // Extract art generation functionality
  const {
    isGenerating,
    generatedImage,
    aspectRatio,
    setAspectRatio,
    errorMessage,
    handleGenerateArt: originalHandleGenerateArt,
  } = useArtGeneration(prompt, title, setTitle);

  // Wrap the generate art function to reset save state
  const handleGenerateArt = () => {
    resetSaveState(); // Reset save state when generating new art
    originalHandleGenerateArt();
  };

  // Extract learning mode toggle
  const { learningMode, toggleLearningMode } = useLearningMode();

  // Extract portfolio save functionality
  const { isSaving, isSaved, portfolioImage, handleSaveToPortfolio, resetSaveState } =
    usePortfolioSave();

  // Create a handler for saving to portfolio that doesn't require parameters
  const handleSaveToPortfolioClick = () => {
    // Pass undefined for userId to let saveUserContent auto-detect the authenticated user
    handleSaveToPortfolio(
      undefined, // Let saveUserContent get the authenticated user automatically
      generatedImage,
      title,
      prompt,
      artStyle,
      aspectRatio
    );
  };

  console.log("ArtCreator: generatedImage =", generatedImage);

  return (
    <div className="container pt-12 pb-16">
      <ArtHeader
        learningMode={learningMode}
        toggleLearningMode={toggleLearningMode}
      />

      <ArtCreatorContent
        prompt={prompt}
        setPrompt={setPrompt}
        title={title}
        setTitle={setTitle}
        handleGenerateArt={handleGenerateArt}
        handleGenerateIdea={handleGenerateIdea}
        handleImprovePrompt={handleImprovePrompt}
        isGenerating={isGenerating}
        isGeneratingIdea={isGeneratingIdea}
        isImprovingPrompt={isImprovingPrompt}
        generatedImage={generatedImage}
        artStyle={artStyle}
        setArtStyle={setArtStyle}
        aspectRatio={aspectRatio}
        setAspectRatio={setAspectRatio}
        errorMessage={errorMessage}
        isSaving={isSaving}
        isSaved={isSaved}
        portfolioImage={portfolioImage}
        handleSaveToPortfolio={handleSaveToPortfolioClick}
        learningMode={learningMode}
      />
    </div>
  );
};

export default ArtCreator;
