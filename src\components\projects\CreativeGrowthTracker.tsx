import React from "react";
import { Book<PERSON><PERSON>, Palette, Music, TrendingUp, Star, Award } from "lucide-react";

interface ContentStats {
    stories: number;
    artwork: number;
    music: number;
    total: number;
}

interface CreativeGrowthTrackerProps {
    contentStats: ContentStats;
}

const CreativeGrowthTracker: React.FC<CreativeGrowthTrackerProps> = ({
    contentStats
}) => {
    const maxGoal = 10; // Base goal for each category

    const trackerItems = [
        {
            icon: BookOpen,
            label: "Stories",
            count: contentStats.stories,
            color: "text-blue-600",
            bgColor: "bg-blue-100",
            progressColor: "bg-blue-500",
            progressBg: "bg-blue-100"
        },
        {
            icon: Palette,
            label: "Artwork",
            count: contentStats.artwork,
            color: "text-green-600",
            bgColor: "bg-green-100",
            progressColor: "bg-green-500",
            progressBg: "bg-green-100"
        },
        {
            icon: Music,
            label: "Music",
            count: contentStats.music,
            color: "text-purple-600",
            bgColor: "bg-purple-100",
            progressColor: "bg-purple-500",
            progressBg: "bg-purple-100"
        }
    ];

    const getProgressPercentage = (count: number) => {
        return Math.min((count / maxGoal) * 100, 100);
    };

    const getProgressStatus = (count: number) => {
        if (count >= maxGoal) return "🏆 Goal Achieved!";
        if (count >= maxGoal * 0.7) return "🔥 Almost there!";
        if (count >= maxGoal * 0.5) return "💪 Great progress!";
        if (count > 0) return "🌟 Keep going!";
        return "🚀 Start creating!";
    };

    return (
        <div className="bg-gradient-to-br from-white to-green-50 border border-green-200 rounded-xl p-6 mb-8 shadow-sm hover:shadow-lg hover:scale-[1.01] transition-all duration-300">
            <div className="flex items-center justify-center gap-2 mb-6">
                <TrendingUp className="h-6 w-6 text-green-600" />
                <h2 className="text-xl font-bold text-gray-900">Creative Growth Tracker</h2>
                <Star className="h-5 w-5 text-green-500" />
            </div>

            <div className="space-y-6">
                {trackerItems.map((item) => {
                    const Icon = item.icon;
                    const progressPercentage = getProgressPercentage(item.count);
                    const progressStatus = getProgressStatus(item.count);

                    return (
                        <div key={item.label} className="space-y-3 p-3 rounded-lg hover:bg-green-50 hover:scale-[1.01] transition-all duration-300 cursor-pointer group">
                            {/* Header */}
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    <div className={`p-2 rounded-lg ${item.bgColor} group-hover:bg-green-200 transition-colors duration-300`}>
                                        <Icon className={`h-5 w-5 ${item.color} group-hover:scale-110 transition-transform duration-300`} />
                                    </div>
                                    <div>
                                        <span className="text-gray-900 font-semibold">{item.label}</span>
                                        <p className="text-sm text-gray-500">{progressStatus}</p>
                                    </div>
                                </div>
                                <div className="text-right">
                                    <span className="text-lg font-bold text-gray-900">{item.count}</span>
                                    <span className="text-sm text-gray-500">/{maxGoal}</span>
                                </div>
                            </div>

                            {/* Progress Bar */}
                            <div className="relative">
                                <div className={`w-full h-3 ${item.progressBg} rounded-full overflow-hidden`}>
                                    <div
                                        className={`h-full ${item.progressColor} rounded-full transition-all duration-500 ease-out relative`}
                                        style={{ width: `${progressPercentage}%` }}
                                    >
                                        {progressPercentage > 0 && (
                                            <div className="absolute inset-0 bg-white/20 animate-pulse"></div>
                                        )}
                                    </div>
                                </div>
                                <div className="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>0</span>
                                    <span className="font-medium">{Math.round(progressPercentage)}%</span>
                                    <span>{maxGoal}</span>
                                </div>
                            </div>
                        </div>
                    );
                })}

                {/* Total Section */}
                <div className="pt-4 border-t border-gray-200">
                    <div className="flex items-center justify-between p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg">
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg">
                                <Award className="h-5 w-5 text-white" />
                            </div>
                            <div>
                                <span className="text-gray-900 font-bold text-lg">Total Creations</span>
                                <p className="text-sm text-gray-600">Your creative journey so far</p>
                            </div>
                        </div>
                        <div className="text-right">
                            <span className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                                {contentStats.total}
                            </span>
                            <p className="text-sm text-gray-500">projects</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CreativeGrowthTracker;
