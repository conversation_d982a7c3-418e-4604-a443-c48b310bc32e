import React, { useState, useEffect } from "react";
import Image from "next/image";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
    X,
    BookOpen,
    Palette,
    Music,
    Download,
    Share2,
    Edit3,
    Award,
    Loader
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";

interface UserContent {
    id: string;
    type: string;
    title: string;
    content_metadata: Record<string, unknown>;
    preview_url: string | null;
    challenge_id: string | null;
    created_at: string;
    updated_at: string;
    challenge?: {
        id: string;
        title: string;
        description: string;
        type: string;
        difficulty: string;
        prompt: string;
    };
    challenge_completion?: {
        id: string;
        completed_at: string;
    };
}

interface ProjectViewerProps {
    projectId: string | null;
    onClose: () => void;
}

const ProjectViewer: React.FC<ProjectViewerProps> = ({ projectId, onClose }) => {
    const [project, setProject] = useState<UserContent | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [editedTitle, setEditedTitle] = useState("");

    // Fetch project details
    const fetchProjectDetails = React.useCallback(async () => {
        if (!projectId) return;

        setIsLoading(true);
        try {
            const response = await fetch(`/api/user-content/${projectId}`, {
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                setProject(data.content);
                setEditedTitle(data.content.title);
            } else {
                toast.error('Failed to load project details');
                onClose();
            }
        } catch (error) {
            console.error('Error fetching project details:', error);
            toast.error('Failed to load project details');
            onClose();
        } finally {
            setIsLoading(false);
        }
    }, [projectId, onClose]);

    useEffect(() => {
        if (projectId) {
            fetchProjectDetails();
        }
    }, [projectId, fetchProjectDetails]);

    const handleSaveTitle = async () => {
        if (!project || !editedTitle.trim()) return;

        try {
            const response = await fetch(`/api/user-content/${project.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    title: editedTitle.trim()
                })
            });

            const data = await response.json();

            if (data.success) {
                setProject(prev => prev ? { ...prev, title: editedTitle.trim() } : null);
                setIsEditing(false);
                toast.success('Project title updated');
            } else {
                toast.error('Failed to update title');
            }
        } catch (error) {
            console.error('Error updating title:', error);
            toast.error('Failed to update title');
        }
    };

    const handleShare = async () => {
        if (!project) return;

        try {
            const response = await fetch('/api/user-content/share', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    contentIds: [project.id],
                    message: `Check out my ${project.type}: ${project.title}`
                })
            });

            const data = await response.json();

            if (data.success) {
                // Copy share URL to clipboard
                await navigator.clipboard.writeText(data.shareUrl);
                toast.success('Share link copied to clipboard!');
            } else {
                toast.error('Failed to generate share link');
            }
        } catch (error) {
            console.error('Error sharing project:', error);
            toast.error('Failed to share project');
        }
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case "story":
                return <BookOpen className="h-6 w-6 text-blue-600" />;
            case "art":
                return <Palette className="h-6 w-6 text-green-600" />;
            case "music":
                return <Music className="h-6 w-6 text-purple-600" />;
            default:
                return <BookOpen className="h-6 w-6 text-gray-600" />;
        }
    };

    const renderContent = () => {
        if (!project) return null;

        switch (project.type) {
            case "story":
                return (
                    <div className="prose max-w-none">
                        <div className="bg-blue-50 p-6 rounded-lg">
                            <h4 className="text-lg font-semibold text-blue-900 mb-3">Story Content</h4>
                            <div className="text-gray-700 whitespace-pre-wrap">
                                {(project.content_metadata as Record<string, unknown>)?.fullContent as string ||
                                 (project.content_metadata as Record<string, unknown>)?.preview as string ||
                                 "Story content not available"}
                            </div>
                        </div>
                    </div>
                );

            case "art":
                return (
                    <div className="space-y-4">
                        {project.preview_url ? (
                            <div className="bg-gray-100 rounded-lg overflow-hidden">
                                <Image
                                    src={project.preview_url}
                                    alt={project.title}
                                    width={800}
                                    height={400}
                                    className="w-full h-auto max-h-96 object-contain"
                                />
                            </div>
                        ) : (
                            <div className="bg-gray-100 rounded-lg p-12 text-center">
                                <Palette className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                <p className="text-gray-500">Artwork preview not available</p>
                            </div>
                        )}
                        {project.content_metadata?.prompt && (
                            <div className="bg-green-50 p-4 rounded-lg">
                                <h4 className="font-semibold text-green-900 mb-2">Art Prompt</h4>
                                <p className="text-green-800">{project.content_metadata.prompt}</p>
                            </div>
                        )}
                    </div>
                );

            case "music":
                return (
                    <div className="space-y-4">
                        {project.preview_url ? (
                            <div className="bg-purple-50 p-6 rounded-lg">
                                <h4 className="font-semibold text-purple-900 mb-4">Music Player</h4>
                                <audio controls className="w-full">
                                    <source src={project.preview_url} type="audio/mpeg" />
                                    Your browser does not support the audio element.
                                </audio>
                            </div>
                        ) : (
                            <div className="bg-gray-100 rounded-lg p-12 text-center">
                                <Music className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                <p className="text-gray-500">Music preview not available</p>
                            </div>
                        )}
                        {project.content_metadata?.description && (
                            <div className="bg-purple-50 p-4 rounded-lg">
                                <h4 className="font-semibold text-purple-900 mb-2">Music Description</h4>
                                <p className="text-purple-800">{project.content_metadata.description}</p>
                            </div>
                        )}
                    </div>
                );

            default:
                return (
                    <div className="bg-gray-100 rounded-lg p-12 text-center">
                        <p className="text-gray-500">Content preview not available</p>
                    </div>
                );
        }
    };

    if (!projectId) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                {isLoading ? (
                    <div className="p-8 text-center">
                        <Loader className="h-8 w-8 animate-spin mx-auto mb-4" />
                        <p>Loading project details...</p>
                    </div>
                ) : project ? (
                    <div className="p-6">
                        {/* Header */}
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center gap-3">
                                {getTypeIcon(project.type)}
                                <div>
                                    {isEditing ? (
                                        <div className="flex items-center gap-2">
                                            <label className="sr-only" htmlFor="project-title">Project Title</label>
                                            <input
                                                id="project-title"
                                                type="text"
                                                value={editedTitle}
                                                onChange={(e) => setEditedTitle(e.target.value)}
                                                className="text-xl font-bold border rounded px-2 py-1"
                                                onKeyPress={(e) => e.key === 'Enter' && handleSaveTitle()}
                                                placeholder="Enter project title"
                                                aria-label="Project title"
                                            />
                                            <Button size="sm" onClick={handleSaveTitle}>Save</Button>
                                            <Button size="sm" variant="outline" onClick={() => setIsEditing(false)}>Cancel</Button>
                                        </div>
                                    ) : (
                                        <h2 className="text-xl font-bold text-gray-900">{project.title}</h2>
                                    )}
                                    <p className="text-sm text-gray-500">
                                        {project.type.charAt(0).toUpperCase() + project.type.slice(1)} • 
                                        Created {format(new Date(project.created_at), "MMM d, yyyy 'at' h:mm a")}
                                    </p>
                                </div>
                            </div>
                            <Button variant="ghost" size="sm" onClick={onClose}>
                                <X className="h-4 w-4" />
                            </Button>
                        </div>

                        {/* Challenge Info */}
                        {project.challenge && (
                            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                                <div className="flex items-center gap-2 mb-2">
                                    <Award className="h-5 w-5 text-orange-600" />
                                    <h3 className="font-semibold text-orange-900">Challenge Project</h3>
                                </div>
                                <p className="text-orange-800 mb-2">{project.challenge.title}</p>
                                <p className="text-sm text-orange-700">{project.challenge.description}</p>
                                {project.challenge_completion && (
                                    <p className="text-xs text-orange-600 mt-2">
                                        Completed {format(new Date(project.challenge_completion.completed_at), "MMM d, yyyy")}
                                    </p>
                                )}
                            </div>
                        )}

                        {/* Content */}
                        <div className="mb-6">
                            {renderContent()}
                        </div>

                        {/* Actions */}
                        <div className="flex flex-wrap gap-3 pt-4 border-t">
                            <Button onClick={() => setIsEditing(true)} variant="outline">
                                <Edit3 className="h-4 w-4 mr-2" />
                                Edit Title
                            </Button>
                            <Button onClick={handleShare} variant="outline">
                                <Share2 className="h-4 w-4 mr-2" />
                                Share
                            </Button>
                            {project.preview_url && (
                                <Button 
                                    onClick={() => window.open(project.preview_url!, '_blank')} 
                                    variant="outline"
                                >
                                    <Download className="h-4 w-4 mr-2" />
                                    Download
                                </Button>
                            )}
                        </div>
                    </div>
                ) : (
                    <div className="p-8 text-center">
                        <p>Project not found</p>
                    </div>
                )}
            </Card>
        </div>
    );
};

export default ProjectViewer;
