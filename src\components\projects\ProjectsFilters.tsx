import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { BookOpen, Palette, Music, Filter, ChevronDown, Check } from "lucide-react";

interface ContentStats {
    stories: number;
    artwork: number;
    music: number;
    total: number;
}

interface ProjectsFiltersProps {
    selectedFilter: string;
    onFilterChange: (filter: string) => void;
    contentStats: ContentStats;
    sortBy: string;
    onSortChange: (sort: string) => void;
}

const ProjectsFilters: React.FC<ProjectsFiltersProps> = ({
    selectedFilter,
    onFilterChange,
    contentStats,
    sortBy,
    onSortChange
}) => {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);

    const sortOptions = [
        { id: "newest", label: "Newest" },
        { id: "oldest", label: "Oldest" },
        { id: "a-z", label: "A-Z" }
    ];

    const filters = [
        {
            id: "all",
            label: "All Projects",
            icon: Filter,
            count: contentStats.total,
            color: "bg-gray-100 text-gray-800"
        },
        {
            id: "story",
            label: "Stories",
            icon: BookOpen,
            count: contentStats.stories,
            color: "bg-blue-100 text-blue-800"
        },
        {
            id: "art",
            label: "Artwork",
            icon: Palette,
            count: contentStats.artwork,
            color: "bg-green-100 text-green-800"
        },
        {
            id: "music",
            label: "Music",
            icon: Music,
            count: contentStats.music,
            color: "bg-purple-100 text-purple-800"
        }
    ];

    return (
        <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
                <div className="flex gap-6">
                    {filters.map((filter) => {
                        const Icon = filter.icon;
                        const isSelected = selectedFilter === filter.id;

                        return (
                            <button
                                key={filter.id}
                                type="button"
                                onClick={() => {
                                    console.log('Filter clicked:', filter.id);
                                    onFilterChange(filter.id);
                                }}
                                className={`flex items-center gap-2 px-4 py-2.5 rounded-lg transition-all duration-200 ${
                                    isSelected
                                        ? "bg-gradient-to-r from-teal-50 to-teal-100 text-teal-700 border-2 border-teal-300 shadow-sm scale-105"
                                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-50 hover:scale-[1.02] border-2 border-transparent"
                                }`}
                            >
                                <Icon className={`h-4 w-4 transition-transform duration-200 ${isSelected ? 'scale-110' : ''}`} />
                                <span className="font-medium">{filter.label}</span>
                                {filter.count > 0 && (
                                    <span className={`ml-1 px-2 py-0.5 text-xs rounded-full ${
                                        isSelected
                                            ? 'bg-teal-200 text-teal-800'
                                            : 'bg-gray-200 text-gray-600'
                                    }`}>
                                        {filter.count}
                                    </span>
                                )}
                            </button>
                        );
                    })}
                </div>

                <div className="relative">
                    <Button
                        variant="outline"
                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                        className="text-teal-600 border-2 border-teal-400 hover:bg-teal-50 flex items-center gap-2 rounded-full px-6 py-2 font-medium"
                    >
                        <Filter className="h-4 w-4" />
                        Sort & Filter
                        <ChevronDown className={`h-4 w-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
                    </Button>

                    {isDropdownOpen && (
                        <div className="absolute right-0 top-full mt-2 w-48 bg-white border border-gray-200 rounded-xl shadow-xl z-50 overflow-hidden">
                            <div className="p-4">
                                <div className="mb-3">
                                    <h4 className="text-sm font-semibold text-gray-700 mb-3">Sort by:</h4>
                                    <div className="space-y-2">
                                        {sortOptions.map((option) => (
                                            <button
                                                key={option.id}
                                                type="button"
                                                onClick={() => {
                                                    onSortChange(option.id);
                                                    setIsDropdownOpen(false);
                                                }}
                                                className={`w-full flex items-center justify-between px-3 py-2.5 text-sm rounded-lg transition-all duration-200 ${
                                                    sortBy === option.id
                                                        ? 'bg-gradient-to-r from-teal-50 to-teal-100 text-teal-700 border border-teal-200'
                                                        : 'text-gray-700 hover:bg-gray-50 hover:scale-[1.02]'
                                                }`}
                                            >
                                                <span className="font-medium">{option.label}</span>
                                                {sortBy === option.id && (
                                                    <div className="flex items-center justify-center w-5 h-5 bg-teal-500 rounded-full">
                                                        <Check className="h-3 w-3 text-white" />
                                                    </div>
                                                )}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Backdrop to close dropdown */}
                    {isDropdownOpen && (
                        <div
                            className="fixed inset-0 z-40"
                            onClick={() => setIsDropdownOpen(false)}
                        />
                    )}
                </div>
            </div>

            {contentStats.total === 0 && (
                <div className="text-center py-12">
                    <p className="text-gray-500 text-lg mb-2">You haven't created any projects yet</p>
                    <p className="text-gray-400 text-sm">
                        Start creating stories, artwork, or coded creatures to see them here
                    </p>
                </div>
            )}
        </div>
    );
};

export default ProjectsFilters;
