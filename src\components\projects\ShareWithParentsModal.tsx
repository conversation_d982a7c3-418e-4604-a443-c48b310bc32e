import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { X, Mail, Send, Loader } from "lucide-react";
import { toast } from "sonner";

interface ShareWithParentsModalProps {
    isOpen: boolean;
    onClose: () => void;
    userStats: Record<string, unknown>;
}

const ShareWithParentsModal: React.FC<ShareWithParentsModalProps> = ({
    isOpen,
    onClose,
    userStats
}) => {
    const [email, setEmail] = useState("");
    const [message, setMessage] = useState("");
    const [isLoading, setIsLoading] = useState(false);

    const handleSendEmail = async () => {
        if (!email.trim()) {
            toast.error("Please enter an email address");
            return;
        }

        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            toast.error("Please enter a valid email address");
            return;
        }

        setIsLoading(true);

        try {
            const response = await fetch('/api/share-portfolio', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    parentEmail: email.trim(),
                    message: message.trim(),
                    includeStats: true,
                    includeProjects: true
                })
            });

            const data = await response.json();

            if (data.success) {
                toast.success("Portfolio shared successfully! Email sent to parent.");
                setEmail("");
                setMessage("");
                onClose();
            } else {
                toast.error(data.error || "Failed to send email");
            }
        } catch (error) {
            console.error('Error sharing portfolio:', error);
            toast.error("Failed to send email. Please try again.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendEmail();
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <Card className="w-full max-w-md">
                <div className="p-6">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-2">
                            <Mail className="h-5 w-5 text-teal-600" />
                            <h2 className="text-lg font-semibold text-gray-900">
                                Share With Parents
                            </h2>
                        </div>
                        <Button variant="ghost" size="sm" onClick={onClose}>
                            <X className="h-4 w-4" />
                        </Button>
                    </div>

                    {/* Content Preview */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-6">
                        <h3 className="font-medium text-gray-900 mb-2">What will be shared:</h3>
                        <ul className="text-sm text-gray-600 space-y-1">
                            <li>• {userStats.total} creative projects</li>
                            <li>• {userStats.stories} stories</li>
                            <li>• {userStats.artwork} artwork pieces</li>
                            <li>• {userStats.music} music compositions</li>
                            <li>• Creative growth progress</li>
                        </ul>
                    </div>

                    {/* Email Input */}
                    <div className="mb-4">
                        <label htmlFor="parentEmail" className="block text-sm font-medium text-gray-700 mb-2">
                            Parent&apos;s Email Address *
                        </label>
                        <input
                            id="parentEmail"
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            onKeyPress={handleKeyPress}
                            placeholder="<EMAIL>"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                            disabled={isLoading}
                        />
                    </div>

                    {/* Message Input */}
                    <div className="mb-6">
                        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                            Personal Message (Optional)
                        </label>
                        <textarea
                            id="message"
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            placeholder="Hi Mom/Dad! Check out my creative projects..."
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none"
                            disabled={isLoading}
                        />
                    </div>

                    {/* Actions */}
                    <div className="flex gap-3">
                        <Button
                            variant="outline"
                            onClick={onClose}
                            className="flex-1"
                            disabled={isLoading}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleSendEmail}
                            className="flex-1 bg-teal-600 hover:bg-teal-700 text-white"
                            disabled={isLoading || !email.trim()}
                        >
                            {isLoading ? (
                                <>
                                    <Loader className="h-4 w-4 mr-2 animate-spin" />
                                    Sending...
                                </>
                            ) : (
                                <>
                                    <Send className="h-4 w-4 mr-2" />
                                    Send Email
                                </>
                            )}
                        </Button>
                    </div>

                    {/* Info */}
                    <p className="text-xs text-gray-500 mt-4 text-center">
                        Your parent will receive an email with a secure link to view your creative portfolio.
                    </p>
                </div>
            </Card>
        </div>
    );
};

export default ShareWithParentsModal;
